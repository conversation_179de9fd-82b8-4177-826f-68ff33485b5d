<div class="relative w-full max-w-sm mx-auto bg-cover bg-center bg-no-repeat rounded-2xl p-6 md:p-8 flex flex-col h-auto min-h-[600px] glow-on-hover transition-all duration-300 hover:scale-105"
     style="background-image: url('assets/images/rectangle.png');">

  <!-- Overlay for better text readability -->
  <div class="absolute inset-0 bg-black/20 rounded-2xl"></div>

  <div class="relative z-10 text-white flex flex-col h-full">
    <!-- Video Section -->
    <div class="w-full aspect-video rounded-xl overflow-hidden mb-4">
      <iframe
        width="100%"
        height="100%"
        src="https://www.youtube.com/embed/qUiD6167jE8"
        title="Kyz Kuu"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerpolicy="strict-origin-when-cross-origin"
        allowfullscreen
        class="rounded-xl"
      ></iframe>
    </div>

    <!-- Content Section -->
    <div class="flex-1 flex flex-col">
      <div class="mb-6">
        <h3 class="font-bold text-xl md:text-2xl mb-1">Ключ активации</h3>
        <h4 class="text-lg md:text-xl mb-2">Kyz kuu</h4>
        <div class="text-sm text-[#31FF57] mb-3">
          эта игра есть в вашей библиотеке
        </div>
        <p class="text-sm leading-relaxed text-gray-200">
          Игра, в которой от громкости голоса игрока зависит скорость бега лошади.
        </p>
      </div>

      <!-- Game Features -->
      <div class="flex flex-wrap gap-2 mb-6">
        <div class="flex items-center gap-2 bg-[#A2845E] px-3 py-2 rounded-lg text-white text-sm">
          <img src="assets/icons/micro.svg" alt="microphone" class="w-4 h-4" />
          <span>2 микрофона</span>
        </div>
        <div class="flex items-center gap-2 bg-[#A2845E] px-3 py-2 rounded-lg text-white text-sm">
          <img src="assets/icons/clock.svg" alt="clock" class="w-4 h-4" />
          <span>15 минут</span>
        </div>
      </div>

      <!-- Price and Action -->
      <div class="mt-auto">
        <div class="flex items-center gap-4 mb-4">
          <div class="text-2xl md:text-3xl font-bold text-[#FFB94A]">300Т</div>
        </div>
        <button class="w-full bg-[#FF9500] hover:bg-[#e8860a] py-3 px-4 font-bold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
                style="box-shadow: 0px 20px 40px 0px #FF95003D;">
          В корзине
        </button>
      </div>
    </div>
  </div>
</div>
