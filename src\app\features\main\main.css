/* Custom animations and effects for the layered site */

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Floating animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Glow effect for cards */
.glow-on-hover {
  transition: all 0.3s ease;
}

.glow-on-hover:hover {
  box-shadow: 0 0 30px rgba(168, 85, 247, 0.4);
}

/* Custom backdrop blur for better browser support */
.backdrop-blur-custom {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Ensure proper layering */
.layer-base {
  position: relative;
  z-index: 1;
}

.layer-content {
  position: relative;
  z-index: 10;
}

.layer-overlay {
  position: relative;
  z-index: 20;
}