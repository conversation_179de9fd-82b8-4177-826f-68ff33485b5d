import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Main } from './features/main/main';
import { Login } from './features/auth/components/login/login';

const routes: Routes = [
  {
    path: '',
    component: Main
  }, 
  {
    path: 'login',
    component: Login
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
