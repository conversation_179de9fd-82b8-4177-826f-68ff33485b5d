<!-- Main Container with Unified Gradient Background -->
<div class="min-h-screen relative overflow-hidden">
  <!-- Unified animated gradient background for entire page -->
  <div class="fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900/70 to-indigo-900"></div>
  <div class="fixed inset-0 animated-gradient opacity-15"></div>
  <div class="fixed inset-0 bg-gradient-to-t from-blue-900/30 via-transparent to-purple-900/20"></div>

  <!-- Content overlay to ensure proper layering -->
  <div class="relative z-10">

  <app-header> </app-header>

  <!-- Hero Section -->
  <section
    class="relative min-h-screen flex items-center justify-center overflow-hidden pt-20"
  >
    <!-- Subtle overlay for content readability -->
    <div
      class="absolute inset-0 bg-black/10"
    ></div>
    <div class="absolute inset-0 opacity-10">
      <div
        class="w-full h-full"
        style="
          background-image: url('data:image/svg+xml;utf8,<svg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><g fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;><g fill=&quot;%239C92AC&quot; fill-opacity=&quot;0.3&quot;><circle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;2&quot;/></g></g></svg>');
        "
      ></div>
    </div>

    <!-- Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <!-- Left Content -->
        <div class="space-y-8 text-center lg:text-left">
          <!-- Main Title -->
          <h1
            class="text-4xl md:text-5xl lg:text-6xl xl:text-6xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 leading-tight animate-gradient"
          >
            Уникальное развлечение для гостей вашего тоя
          </h1>

          <!-- Description -->
          <p
            class="text-lg md:text-xl text-gray-300 max-w-2xl mx-auto lg:mx-0 leading-relaxed"
          >
            Интерактивные игры и развлечения, которые сделают ваше мероприятие
            незабываемым
          </p>

          <!-- Action Buttons -->
          <div
            class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
          >
            <button
              class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-full hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 font-semibold text-lg shadow-lg"
            >
              Начать играть
            </button>
            <button
              class="border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-full hover:bg-purple-400 hover:text-white transition-all font-semibold text-lg"
            >
              Смотреть демо
            </button>
          </div>

          <!-- Decorative Icons -->
          <div
            class="flex items-center justify-center lg:justify-start gap-4 mt-8"
          >
            <img
              src="/assets/icons/heart%20pink.png"
              class="w-16 h-16 md:w-20 md:h-20 animate-float"
              alt=""
              style="animation-delay: 0s"
            />
            <img
              src="/assets/icons/game%20blue.png"
              class="w-16 h-16 md:w-20 md:h-20 animate-float"
              alt=""
              style="animation-delay: 1s"
            />
            <img
              src="/assets/icons/heart%20pink.png"
              class="w-16 h-16 md:w-20 md:h-20 animate-float"
              alt=""
              style="animation-delay: 2s"
            />
            <img
              src="/assets/icons/game%20blue.png"
              class="w-16 h-16 md:w-20 md:h-20 animate-float"
              alt=""
              style="animation-delay: 3s"
            />
          </div>
        </div>

        <!-- Right Content -->
        <div class="relative flex justify-center lg:justify-end">
          <img
            src="assets/images/main3.png"
            alt="Game Preview"
            class="max-w-full h-auto relative z-10 drop-shadow-2xl"
          />

          <!-- Decorative Elements around image -->
          <div
            class="absolute -top-10 -left-10 w-20 h-20 bg-purple-500/30 rounded-full blur-xl animate-pulse"
          ></div>
          <div
            class="absolute -bottom-10 -right-10 w-24 h-24 bg-pink-500/30 rounded-full blur-xl animate-pulse delay-1000"
          ></div>
        </div>
      </div>
    </div>

    <!-- Enhanced Floating Elements -->
    <div
      class="absolute top-1/4 left-10 w-24 h-24 bg-purple-500/20 rounded-full blur-xl animate-pulse"
    ></div>
    <div
      class="absolute bottom-1/4 right-10 w-32 h-32 bg-pink-500/20 rounded-full blur-xl animate-pulse delay-1000"
    ></div>
    <div
      class="absolute top-1/2 left-1/4 w-20 h-20 bg-blue-500/20 rounded-full blur-xl animate-pulse delay-500"
    ></div>
    <div
      class="absolute top-3/4 right-1/4 w-16 h-16 bg-cyan-500/20 rounded-full blur-xl animate-pulse delay-2000"
    ></div>

    <!-- Additional decorative elements -->
    <div
      class="absolute top-10 right-1/3 w-2 h-2 bg-white rounded-full animate-ping"
    ></div>
    <div
      class="absolute bottom-20 left-1/3 w-1 h-1 bg-purple-400 rounded-full animate-ping delay-1000"
    ></div>
    <div
      class="absolute top-1/3 right-10 w-1.5 h-1.5 bg-pink-400 rounded-full animate-ping delay-2000"
    ></div>
  </section>

  <!-- Games Section -->
  <section
    id="games"
    class="relative py-24"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Title -->
      <div class="text-center mb-20">
        <h2
          class="text-4xl md:text-5xl lg:text-6xl font-black text-[#40A3FF] mb-6 tracking-tight"
        >
          НАШИ ИГРЫ
        </h2>
        <p
          class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
        >
          Выберите идеальную игру для вашего мероприятия
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto mt-8 rounded-full"
        ></div>
      </div>

      <!-- Games Grid -->
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12"
      >
        <app-card></app-card>
        <app-card></app-card>
        <app-card></app-card>
      </div>
    </div>
  </section>

  <section class="relative py-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2
        class="text-4xl md:text-6xl font-black text-center uppercase text-[#40A3FF] mb-4"
      >
        как играют в Kys Kuu
      </h2>

      <!--      #images-->
      <div class="relative">
        <img
          src="/assets/icons/pink-ornament.png"
          class="absolute size-64 -left-64"
          alt=""
        />
        <img
          src="/assets/icons/star.svg"
          class="absolute size-52 top-0 -left-52"
          alt=""
        />

        <img
          src="/assets/icons/game%20blue.png"
          class="absolute size-64 -right-64"
          alt=""
        />
        <img
          src="/assets/icons/pink-ornament2.svg"
          class="absolute size-52 -top-0 -right-52"
          alt=""
        />
      </div>

      <div class="flex justify-center">
        <iframe
          width="1200"
          height="650"
          src="https://www.youtube.com/embed/bSSxPQEuSz0"
          title="Еркеш Хасен, Қанай - Қызық Times | Ең көңілді дуэт | Пародия | Ханшайым | Премьера | Қызық Live"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          referrerpolicy="strict-origin-when-cross-origin"
          allowfullscreen
        ></iframe>
      </div>
      <div class="text-center text-white text-lg mt-5">
        Отрывок из выпуска “Кызык Times” на телеканале «Хабар». Гости передачи
        Еркеш Хасен и QANAY попробовали нашу игру.
      </div>
    </div>
  </section>

  <!--  divider-->
  <div class="flex items-center justify-center my-10">
    <img src="assets/icons/ornament.png" class="w-64" alt="" />
    <img src="assets/icons/ornament.png" class="w-64" alt="" />
    <img src="assets/icons/ornament.png" class="w-64" alt="" />
    <img src="assets/icons/ornament.png" class="w-64" alt="" />
    <img src="assets/icons/ornament.png" class="w-64" alt="" />
    <img src="assets/icons/ornament.png" class="w-64" alt="" />
    <img src="assets/icons/ornament.png" class="w-64" alt="" />
  </div>

  <!-- About Us Section -->
  <section
    id="about"
    class="relative py-24"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Title -->
      <div class="text-center mb-16">
        <h2
          class="text-4xl md:text-5xl lg:text-6xl font-black text-center uppercase text-[#40A3FF] mb-6 tracking-tight"
        >
          Привет, это студия ToyForToi
        </h2>
        <div
          class="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto rounded-full"
        ></div>
      </div>

      <!-- Description -->
      <div class="text-center mb-16">
        <p
          class="text-lg md:text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed"
        >
          Мы — молодая стартап-команда из Казахстана, которая разрабатывает
          интерактивные решения для мероприятий. Мы создаём игры, которые
          украсят любой той. Мы за то, чтобы развитие технологий сохраняло
          традиции народа.
        </p>
      </div>

      <!-- Video Placeholder -->
      <div class="flex justify-center">
        <div class="relative w-full max-w-6xl">
          <div
            class="relative pb-[56.25%] h-0 overflow-hidden rounded-2xl shadow-2xl bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700"
          >
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="text-center">
                <div
                  class="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center"
                >
                  <svg
                    class="w-8 h-8 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"
                    />
                  </svg>
                </div>
                <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">
                  Видео о нашей студии
                </h3>
                <p class="text-gray-400">Скоро здесь будет интересное видео</p>
              </div>
            </div>
          </div>

          <!-- Video Glow Effect -->
          <div
            class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-xl -z-10 transform scale-105"
          ></div>
        </div>
      </div>
    </div>
  </section>

  <!-- Elegant Divider -->
  <div class="relative py-16 overflow-hidden">
    <div class="absolute inset-0 flex items-center justify-center">
      <div
        class="w-full max-w-6xl flex items-center justify-center space-x-4 opacity-30"
      >
        <img
          src="assets/icons/ornament.png"
          class="w-16 md:w-20 lg:w-24 animate-float"
          alt=""
          style="animation-delay: 0s"
        />
        <img
          src="assets/icons/ornament.png"
          class="w-16 md:w-20 lg:w-24 animate-float"
          alt=""
          style="animation-delay: 0.5s"
        />
        <img
          src="assets/icons/ornament.png"
          class="w-16 md:w-20 lg:w-24 animate-float"
          alt=""
          style="animation-delay: 1s"
        />
        <img
          src="assets/icons/ornament.png"
          class="w-16 md:w-20 lg:w-24 animate-float"
          alt=""
          style="animation-delay: 1.5s"
        />
        <img
          src="assets/icons/ornament.png"
          class="w-16 md:w-20 lg:w-24 animate-float"
          alt=""
          style="animation-delay: 2s"
        />
        <img
          src="assets/icons/ornament.png"
          class="w-16 md:w-20 lg:w-24 animate-float"
          alt=""
          style="animation-delay: 2.5s"
        />
        <img
          src="assets/icons/ornament.png"
          class="w-16 md:w-20 lg:w-24 animate-float"
          alt=""
          style="animation-delay: 3s"
        />
      </div>
    </div>
    <div class="relative z-10 flex justify-center">
      <div
        class="w-32 h-px bg-gradient-to-r from-transparent via-purple-400 to-transparent"
      ></div>
    </div>
  </div>

  <!-- Benefits Section -->
  <section
    class="relative py-24"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Title -->
      <div class="text-center mb-20">
        <h2
          class="text-4xl md:text-5xl lg:text-6xl font-black text-center uppercase text-[#40A3FF] mb-6 tracking-tight"
        >
          ПРЕИМУЩЕСТВА
        </h2>
        <div
          class="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto rounded-full"
        ></div>
      </div>

      <!-- Benefits Image -->
      <div class="flex justify-center">
        <div class="relative">
          <img
            src="assets/images/benefits.png"
            alt="Преимущества"
            class="max-w-full h-auto rounded-2xl shadow-2xl"
          />

          <!-- Image Glow Effect -->
          <div
            class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-xl -z-10 transform scale-105"
          ></div>
        </div>
      </div>
    </div>
  </section>

  <!-- Pricing Section -->
  <section
    id="pricing"
    class="relative py-24"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Title -->
      <div class="text-center mb-20">
        <h2
          class="text-4xl md:text-5xl lg:text-6xl font-black text-center uppercase text-[#40A3FF] mb-6 tracking-tight"
        >
          Тарифы
        </h2>
        <p
          class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
        >
          Выберите подходящий тариф для вашего мероприятия
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-purple-400 to-pink-400 mx-auto mt-8 rounded-full"
        ></div>
      </div>

      <!-- Pricing Cards -->
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12"
      >
        <app-tarrifs-card />
        <app-tarrifs-card />
        <app-tarrifs-card />
      </div>
    </div>
  </section>

  <!-- How It Works Section -->
  <section
    class="relative py-20"
  >
    <div class="h-fit">
      <img
        src="/assets/images/left-img.png"
        class="absolute"
        style="width: 500px"
        alt=""
      />
      <img
        src="/assets/images/right-img.png"
        class="absolute right-0"
        style="width: 500px"
        alt=""
      />
    </div>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Title -->
      <div class="text-center mb-16">
        <h2
          class="text-4xl md:text-6xl font-black text-center uppercase text-[#40A3FF] mb-4"
        >
          КАК ЭТО РАБОТАЕТ?
        </h2>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
          Простые шаги для незабываемого развлечения
        </p>
      </div>

      <!-- Steps -->
      <div class="space-y-12">
        <!-- Step 1 -->
        <div class="text-center group">
          <div class="relative mb-6">
            <div
              class="w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-6xl font-bold text-white shadow-lg shadow-cyan-500/50 group-hover:shadow-cyan-500/70 transition-all duration-300 transform group-hover:scale-110"
            >
              1
            </div>
            <div
              class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full opacity-20 animate-pulse"
            ></div>
          </div>
          <h3 class="text-xl font-bold text-[#31B9CC] mb-3 uppercase">
            Покупаете игру
          </h3>
          <p class="text-gray-300 text-base leading-relaxed">
            Вам предоставляется игра и ключи для активации игры.
            <br />
            Также в стоимость входят устройства (микрофоны либо контроллеры).
          </p>
        </div>
        <div class="text-center group">
          <div class="relative mb-6">
            <div
              class="w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-6xl font-bold text-white shadow-lg shadow-cyan-500/50 group-hover:shadow-cyan-500/70 transition-all duration-300 transform group-hover:scale-110"
            >
              2
            </div>
            <div
              class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full opacity-20 animate-pulse"
            ></div>
          </div>
          <h3 class="text-xl font-bold text-[#31B9CC] mb-3 uppercase">
            Активируете игру
          </h3>
          <p class="text-gray-300 text-base leading-relaxed">
            При запуске игры у вас запросит ключ активации.

            <br />
            В течение 10 часов можно сколько угодно играть и перезапускать.
            <br />
            (если у вас есть подписка, то ключ действует месяц)
          </p>
        </div>
        <div class="text-center group">
          <div class="relative mb-6">
            <div
              class="w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-6xl font-bold text-white shadow-lg shadow-cyan-500/50 group-hover:shadow-cyan-500/70 transition-all duration-300 transform group-hover:scale-110"
            >
              3
            </div>
            <div
              class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full opacity-20 animate-pulse"
            ></div>
          </div>
          <h3 class="text-xl font-bold text-[#31B9CC] mb-3 uppercase">
            играйте на тое
          </h3>
          <p class="text-gray-300 text-base leading-relaxed">
            Подключите необходимые устройства на месте проведения и
            <br />
            используйте второй ключ для активации игры.

            <br />
            Если у вас есть подписка, второй ключ не нужен.
          </p>
        </div>
        <div class="text-center group">
          <div class="relative mb-6">
            <div
              class="w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-6xl font-bold text-white shadow-lg shadow-cyan-500/50 group-hover:shadow-cyan-500/70 transition-all duration-300 transform group-hover:scale-110"
            >
              4
            </div>
            <div
              class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full opacity-20 animate-pulse"
            ></div>
          </div>
          <h3 class="text-xl font-bold text-[#31B9CC] mb-3 uppercase">
            Покупаете ключи
          </h3>
          <p class="text-gray-300 text-base leading-relaxed">
            Игра и устройства навсегда ваши. Перед следующим тоем
            <br />
            не забудьте приобрести новый ключ.
            <br />
            Если у вас подписка, то обновляете ключи раз в месяц.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="relative py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Title -->
      <div class="text-center mb-16">
        <h2
          class="text-4xl md:text-6xl font-black text-center uppercase text-[#40A3FF] mb-4"
        >
          ЧАСТО ЗАДАВАЕМЫЕ ВОПРОСЫ
        </h2>

        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
          Ответы на популярные вопросы о наших играх
        </p>
      </div>

      <!-- FAQ Items -->
      <div class="space-y-4">
        <!-- FAQ Item 1 -->
        <div
          class="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-2xl border border-gray-600/30 backdrop-blur-sm"
        >
          <button
            class="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-colors rounded-2xl"
          >
            <span class="text-lg font-semibold text-white"
              >Какое оборудование нужно для игр?</span
            >
            <svg
              class="w-6 h-6 text-purple-400 transform transition-transform"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </button>
          <div class="px-6 pb-6">
            <p class="text-gray-300 leading-relaxed">
              Для большинства игр достаточно ноутбука или ПК с микрофонами.
              Некоторые игры могут требовать геймпады или дополнительные
              устройства.
            </p>
          </div>
        </div>

        <!-- FAQ Item 2 -->
        <div
          class="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-2xl border border-gray-600/30 backdrop-blur-sm"
        >
          <button
            class="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-colors rounded-2xl"
          >
            <span class="text-lg font-semibold text-white"
              >Сколько человек может играть одновременно?</span
            >
            <svg
              class="w-6 h-6 text-purple-400 transform transition-transform"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </button>
          <div class="px-6 pb-6">
            <p class="text-gray-300 leading-relaxed">
              Количество игроков зависит от конкретной игры. Обычно от 2 до 8
              человек могут участвовать одновременно.
            </p>
          </div>
        </div>

        <!-- FAQ Item 3 -->
        <div
          class="bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-2xl border border-gray-600/30 backdrop-blur-sm"
        >
          <button
            class="w-full p-6 text-left flex justify-between items-center hover:bg-gray-700/20 transition-colors rounded-2xl"
          >
            <span class="text-lg font-semibold text-white"
              >Как долго действует ключ активации?</span
            >
            <svg
              class="w-6 h-6 text-purple-400 transform transition-transform"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </button>
          <div class="px-6 pb-6">
            <p class="text-gray-300 leading-relaxed">
              Ключ активации действует 10 часов с момента первого запуска игры.
              В течение этого времени можно играть неограниченно.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="relative bg-black py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="col-span-1 md:col-span-2">
          <div class="text-3xl font-bold text-white mb-4">TOY FOR TOI</div>
          <p class="text-gray-400 mb-6 max-w-md">
            Создаем незабываемые моменты с помощью интерактивных игр и
            развлечений для ваших мероприятий.
          </p>
          <div class="flex space-x-4">
            <a
              href="#"
              class="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center hover:from-purple-700 hover:to-pink-700 transition-all"
            >
              <span class="text-white text-sm">f</span>
            </a>
            <a
              href="#"
              class="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center hover:from-purple-700 hover:to-pink-700 transition-all"
            >
              <span class="text-white text-sm">t</span>
            </a>
            <a
              href="#"
              class="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center hover:from-purple-700 hover:to-pink-700 transition-all"
            >
              <span class="text-white text-sm">i</span>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h3 class="text-white font-semibold mb-4">Быстрые ссылки</h3>
          <ul class="space-y-2">
            <li>
              <a
                href="#"
                class="text-gray-400 hover:text-purple-400 transition-colors"
                >Главная</a
              >
            </li>
            <li>
              <a
                href="#"
                class="text-gray-400 hover:text-purple-400 transition-colors"
                >Игры</a
              >
            </li>
            <li>
              <a
                href="#"
                class="text-gray-400 hover:text-purple-400 transition-colors"
                >О нас</a
              >
            </li>
            <li>
              <a
                href="#"
                class="text-gray-400 hover:text-purple-400 transition-colors"
                >Контакты</a
              >
            </li>
          </ul>
        </div>

        <!-- Contact -->
        <div>
          <h3 class="text-white font-semibold mb-4">Контакты</h3>
          <ul class="space-y-2">
            <li class="text-gray-400">+7 (XXX) XXX-XX-XX</li>
            <li class="text-gray-400">info&#64;toyfortoi.com</li>
            <li class="text-gray-400">г. Алматы, Казахстан</li>
          </ul>
        </div>
      </div>

      <!-- Copyright -->
      <div class="border-t border-gray-800 mt-8 pt-8 text-center">
        <p class="text-gray-400">© 2024 TOY FOR TOI. Все права защищены.</p>
      </div>
    </div>
  </footer>

  </div> <!-- End content overlay -->
</div> <!-- End main container -->
